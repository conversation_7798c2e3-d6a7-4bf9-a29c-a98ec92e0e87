{"type": "server", "timestamp": "2025-06-24T12:07:20,082-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "version[7.17.15], pid[86945], build[default/tar/0b8ecfb4378335f4689c4223d1f1115f16bef3ba/2023-11-10T22:03:46.987399016Z], OS[Mac OS X/15.5/aarch64], JVM[Oracle Corporation/OpenJDK 64-Bit Server VM/21.0.1/21.0.1+12-29]" }
{"type": "server", "timestamp": "2025-06-24T12:07:20,085-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "JVM home [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/jdk.app/Contents/Home], using bundled JDK [true]" }
{"type": "server", "timestamp": "2025-06-24T12:07:20,085-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "JVM arguments [-Xshare:auto, -Des.networkaddress.cache.ttl=60, -Des.networkaddress.cache.negative.ttl=10, -XX:+AlwaysPreTouch, -Xss1m, -Djava.awt.headless=true, -Dfile.encoding=UTF-8, -Djna.nosys=true, -XX:-OmitStackTraceInFastThrow, -XX:+ShowCodeDetailsInExceptionMessages, -Dio.netty.noUnsafe=true, -Dio.netty.noKeySetOptimization=true, -Dio.netty.recycler.maxCapacityPerThread=0, -Dio.netty.allocator.numDirectArenas=0, -Dlog4j.shutdownHookEnabled=false, -Dlog4j2.disable.jmx=true, -Dlog4j2.formatMsgNoLookups=true, -Djava.locale.providers=SPI,COMPAT, --add-opens=java.base/java.io=ALL-UNNAMED, -Djava.security.manager=allow, -XX:+UseG1GC, -Djava.io.tmpdir=/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-16989071375096480540, -XX:+HeapDumpOnOutOfMemoryError, -XX:+ExitOnOutOfMemoryError, -XX:HeapDumpPath=data, -XX:ErrorFile=logs/hs_err_pid%p.log, -Xlog:gc*,gc+age=trace,safepoint:file=logs/gc.log:utctime,pid,tags:filecount=32,filesize=64m, -Xms31744m, -Xmx31744m, -XX:MaxDirectMemorySize=16642998272, -XX:InitiatingHeapOccupancyPercent=30, -XX:G1ReservePercent=25, -Des.path.home=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15, -Des.path.conf=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config, -Des.distribution.flavor=default, -Des.distribution.type=tar, -Des.bundled_jdk=true]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,700-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [aggs-matrix-stats]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,701-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [analysis-common]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,701-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [constant-keyword]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,701-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [frozen-indices]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,701-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [ingest-common]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,701-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [ingest-geoip]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,701-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [ingest-user-agent]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,701-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [kibana]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [lang-expression]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [lang-mustache]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [lang-painless]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [legacy-geo]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [mapper-extras]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [mapper-version]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [parent-join]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,702-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [percolator]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [rank-eval]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [reindex]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [repositories-metering-api]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [repository-encrypted]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [repository-url]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [runtime-fields-common]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [search-business-rules]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,703-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [searchable-snapshots]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,704-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [snapshot-repo-test-kit]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,704-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [spatial]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,704-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [transform]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,704-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [transport-netty4]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,704-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [unsigned-long]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,704-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [vector-tile]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [vectors]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [wildcard]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-aggregate-metric]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-analytics]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-async]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-async-search]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-autoscaling]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,705-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ccr]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-core]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-data-streams]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-deprecation]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-enrich]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-eql]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-fleet]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-graph]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-identity-provider]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ilm]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,706-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-logstash]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ml]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-monitoring]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ql]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-rollup]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-security]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-shutdown]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-sql]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-stack]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-text-structure]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,707-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-voting-only-node]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,708-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-watcher]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,708-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded plugin [analysis-icu]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,708-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded plugin [analysis-smartcn]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,728-04:00", "level": "INFO", "component": "o.e.e.NodeEnvironment", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "using [1] data paths, mounts [[/System/Volumes/Data (/dev/disk3s5)]], net usable_space [231.4gb], net total_space [926.3gb], types [apfs]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,728-04:00", "level": "INFO", "component": "o.e.e.NodeEnvironment", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "heap size [31gb], compressed ordinary object pointers [true]" }
{"type": "server", "timestamp": "2025-06-24T12:07:21,743-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "node name [seo-node-1], node ID [PdaVxQnMS_iyma-tW5xrGA], cluster name [ai-seo-king], roles [master, data, ingest]" }
{"type": "server", "timestamp": "2025-06-24T12:07:22,964-04:00", "level": "WARN", "component": "o.e.c.s.SettingsModule", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "\n*************************************************************************************\nFound index level settings on node level configuration.\n\nSince elasticsearch 5.x index level settings can NOT be set on the nodes \nconfiguration like the elasticsearch.yaml, in system properties or command line \narguments.In order to upgrade all indices the settings must be updated via the \n/${index}/_settings API. Unless all settings are dynamic all indices must be closed \nin order to apply the upgradeIndices created in the future should use index templates \nto set default values. \n\nPlease ensure all required values are updated on all indices by executing: \n\ncurl -XPUT 'http://localhost:9200/_all/_settings?preserve_existing=true' -d '{\n  \"index.number_of_replicas\" : \"0\",\n  \"index.number_of_shards\" : \"1\"\n}'\n*************************************************************************************\n" }
{"type": "server", "timestamp": "2025-06-24T12:07:22,966-04:00", "level": "ERROR", "component": "o.e.b.Bootstrap", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "Exception", 
"stacktrace": ["java.lang.IllegalArgumentException: node settings must not contain any index level settings",
"at org.elasticsearch.common.settings.SettingsModule.<init>(SettingsModule.java:131) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.node.Node.<init>(Node.java:502) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.node.Node.<init>(Node.java:309) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Bootstrap$5.<init>(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Bootstrap.setup(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Bootstrap.init(Bootstrap.java:434) [elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.init(Elasticsearch.java:169) [elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.execute(Elasticsearch.java:160) [elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.cli.EnvironmentAwareCommand.execute(EnvironmentAwareCommand.java:77) [elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.cli.Command.mainWithoutErrorHandling(Command.java:112) [elasticsearch-cli-7.17.15.jar:7.17.15]",
"at org.elasticsearch.cli.Command.main(Command.java:77) [elasticsearch-cli-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:125) [elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:80) [elasticsearch-7.17.15.jar:7.17.15]"] }
{"type": "server", "timestamp": "2025-06-24T12:07:22,969-04:00", "level": "ERROR", "component": "o.e.b.ElasticsearchUncaughtExceptionHandler", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "uncaught exception in thread [main]", 
"stacktrace": ["org.elasticsearch.bootstrap.StartupException: java.lang.IllegalArgumentException: node settings must not contain any index level settings",
"at org.elasticsearch.bootstrap.Elasticsearch.init(Elasticsearch.java:173) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.execute(Elasticsearch.java:160) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.cli.EnvironmentAwareCommand.execute(EnvironmentAwareCommand.java:77) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.cli.Command.mainWithoutErrorHandling(Command.java:112) ~[elasticsearch-cli-7.17.15.jar:7.17.15]",
"at org.elasticsearch.cli.Command.main(Command.java:77) ~[elasticsearch-cli-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:125) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:80) ~[elasticsearch-7.17.15.jar:7.17.15]",
"Caused by: java.lang.IllegalArgumentException: node settings must not contain any index level settings",
"at org.elasticsearch.common.settings.SettingsModule.<init>(SettingsModule.java:131) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.node.Node.<init>(Node.java:502) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.node.Node.<init>(Node.java:309) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Bootstrap$5.<init>(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Bootstrap.setup(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Bootstrap.init(Bootstrap.java:434) ~[elasticsearch-7.17.15.jar:7.17.15]",
"at org.elasticsearch.bootstrap.Elasticsearch.init(Elasticsearch.java:169) ~[elasticsearch-7.17.15.jar:7.17.15]",
"... 6 more"] }
{"type": "server", "timestamp": "2025-06-24T12:13:29,894-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "version[7.17.15], pid[92363], build[default/tar/0b8ecfb4378335f4689c4223d1f1115f16bef3ba/2023-11-10T22:03:46.987399016Z], OS[Mac OS X/15.5/aarch64], JVM[Oracle Corporation/OpenJDK 64-Bit Server VM/21.0.1/21.0.1+12-29]" }
{"type": "server", "timestamp": "2025-06-24T12:13:29,897-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "JVM home [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/jdk.app/Contents/Home], using bundled JDK [true]" }
{"type": "server", "timestamp": "2025-06-24T12:13:29,897-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "JVM arguments [-Xshare:auto, -Des.networkaddress.cache.ttl=60, -Des.networkaddress.cache.negative.ttl=10, -XX:+AlwaysPreTouch, -Xss1m, -Djava.awt.headless=true, -Dfile.encoding=UTF-8, -Djna.nosys=true, -XX:-OmitStackTraceInFastThrow, -XX:+ShowCodeDetailsInExceptionMessages, -Dio.netty.noUnsafe=true, -Dio.netty.noKeySetOptimization=true, -Dio.netty.recycler.maxCapacityPerThread=0, -Dio.netty.allocator.numDirectArenas=0, -Dlog4j.shutdownHookEnabled=false, -Dlog4j2.disable.jmx=true, -Dlog4j2.formatMsgNoLookups=true, -Djava.locale.providers=SPI,COMPAT, --add-opens=java.base/java.io=ALL-UNNAMED, -Djava.security.manager=allow, -XX:+UseG1GC, -Djava.io.tmpdir=/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891, -XX:+HeapDumpOnOutOfMemoryError, -XX:+ExitOnOutOfMemoryError, -XX:HeapDumpPath=data, -XX:ErrorFile=logs/hs_err_pid%p.log, -Xlog:gc*,gc+age=trace,safepoint:file=logs/gc.log:utctime,pid,tags:filecount=32,filesize=64m, -Xms2g, -Xmx2g, -XX:MaxDirectMemorySize=1g, -XX:InitiatingHeapOccupancyPercent=30, -XX:G1ReservePercent=25, -XX:MaxGCPauseMillis=100, -XX:G1HeapRegionSize=4m, -Des.path.home=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15, -Des.path.conf=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config, -Des.distribution.flavor=default, -Des.distribution.type=tar, -Des.bundled_jdk=true]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,245-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [aggs-matrix-stats]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,245-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [analysis-common]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,245-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [constant-keyword]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,245-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [frozen-indices]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [ingest-common]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [ingest-geoip]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [ingest-user-agent]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [kibana]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [lang-expression]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [lang-mustache]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [lang-painless]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,246-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [legacy-geo]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [mapper-extras]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [mapper-version]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [parent-join]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [percolator]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [rank-eval]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [reindex]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [repositories-metering-api]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,247-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [repository-encrypted]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,248-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [repository-url]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,248-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [runtime-fields-common]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,248-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [search-business-rules]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,248-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [searchable-snapshots]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,248-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [snapshot-repo-test-kit]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,248-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [spatial]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [transform]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [transport-netty4]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [unsigned-long]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [vector-tile]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [vectors]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [wildcard]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-aggregate-metric]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-analytics]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,249-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-async]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,250-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-async-search]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,250-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-autoscaling]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,250-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ccr]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,250-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-core]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,250-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-data-streams]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,251-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-deprecation]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,251-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-enrich]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,251-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-eql]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,251-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-fleet]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,252-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-graph]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,252-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-identity-provider]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,252-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ilm]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,252-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-logstash]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,252-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ml]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,252-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-monitoring]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,252-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-ql]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,253-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-rollup]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,253-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-security]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,253-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-shutdown]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,253-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-sql]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,253-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-stack]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,253-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-text-structure]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,253-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-voting-only-node]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,254-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded module [x-pack-watcher]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,254-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded plugin [analysis-icu]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,254-04:00", "level": "INFO", "component": "o.e.p.PluginsService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "loaded plugin [analysis-smartcn]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,272-04:00", "level": "INFO", "component": "o.e.e.NodeEnvironment", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "using [1] data paths, mounts [[/System/Volumes/Data (/dev/disk3s5)]], net usable_space [231.3gb], net total_space [926.3gb], types [apfs]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,273-04:00", "level": "INFO", "component": "o.e.e.NodeEnvironment", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "heap size [2gb], compressed ordinary object pointers [true]" }
{"type": "server", "timestamp": "2025-06-24T12:13:31,285-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "node name [seo-node-1], node ID [bmhpVyXASJ2JME1Pah6BeQ], cluster name [ai-seo-king], roles [transform, data_frozen, master, remote_cluster_client, data, ml, data_content, data_hot, data_warm, data_cold, ingest]" }
{"type": "server", "timestamp": "2025-06-24T12:13:33,405-04:00", "level": "INFO", "component": "o.e.x.m.p.l.CppLogMessageHandler", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "[controller/92403] [Main.cc@122] controller (64 bit): Version 7.17.15 (Build 8285074a0b4035) Copyright (c) 2023 Elasticsearch BV" }
{"type": "server", "timestamp": "2025-06-24T12:13:33,639-04:00", "level": "INFO", "component": "o.e.i.g.ConfigDatabases", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "initialized default databases [[GeoLite2-Country.mmdb, GeoLite2-City.mmdb, GeoLite2-ASN.mmdb]], config databases [[]] and watching [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config/ingest-geoip] for changes" }
{"type": "server", "timestamp": "2025-06-24T12:13:33,640-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseNodeService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "initialized database registry, using geoip-databases directory [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ]" }
{"type": "server", "timestamp": "2025-06-24T12:13:33,913-04:00", "level": "INFO", "component": "o.e.t.NettyAllocator", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "creating NettyAllocator with the following configs: [name=elasticsearch_configured, chunk_size=1mb, suggested_max_allocation_size=1mb, factors={es.unsafe.use_netty_default_chunk_and_page_size=false, g1gc_enabled=true, g1gc_region_size=4mb}]" }
{"type": "server", "timestamp": "2025-06-24T12:13:33,931-04:00", "level": "INFO", "component": "o.e.i.r.RecoverySettings", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "using rate limit [40mb] with [default=40mb, read=0b, write=0b, max=0b]" }
{"type": "server", "timestamp": "2025-06-24T12:13:33,949-04:00", "level": "INFO", "component": "o.e.d.DiscoveryModule", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "using discovery type [single-node] and seed hosts providers [settings]" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,198-04:00", "level": "INFO", "component": "o.e.g.DanglingIndicesState", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "gateway.auto_import_dangling_indices is disabled, dangling indices will not be automatically detected or imported and must be managed manually" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,506-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "initialized" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,506-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "starting ..." }
{"type": "server", "timestamp": "2025-06-24T12:13:34,534-04:00", "level": "INFO", "component": "o.e.x.s.c.f.PersistentCache", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "persistent cache index loaded" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,535-04:00", "level": "INFO", "component": "o.e.x.d.l.DeprecationIndexingComponent", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "deprecation component started" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,597-04:00", "level": "INFO", "component": "o.e.t.TransportService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "publish_address {*************:9300}, bound_addresses {[::]:9300}" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,604-04:00", "level": "INFO", "component": "o.e.x.m.Monitoring", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "creating template [.monitoring-alerts-7] with version [7]" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,607-04:00", "level": "INFO", "component": "o.e.x.m.Monitoring", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "creating template [.monitoring-es] with version [7]" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,608-04:00", "level": "INFO", "component": "o.e.x.m.Monitoring", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "creating template [.monitoring-kibana] with version [7]" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,610-04:00", "level": "INFO", "component": "o.e.x.m.Monitoring", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "creating template [.monitoring-logstash] with version [7]" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,612-04:00", "level": "INFO", "component": "o.e.x.m.Monitoring", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "creating template [.monitoring-beats] with version [7]" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,714-04:00", "level": "INFO", "component": "o.e.c.c.Coordinator", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "setting initial configuration to VotingConfiguration{bmhpVyXASJ2JME1Pah6BeQ}" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,823-04:00", "level": "INFO", "component": "o.e.c.s.MasterService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "elected-as-master ([1] nodes joined)[{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{60RTttZ5Rc2TppAYjTVxqw}{*************}{*************:9300}{cdfhilmrstw} elect leader, _BECOME_MASTER_TASK_, _FINISH_ELECTION_], term: 1, version: 1, delta: master node changed {previous [], current [{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{60RTttZ5Rc2TppAYjTVxqw}{*************}{*************:9300}{cdfhilmrstw}]}" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,855-04:00", "level": "INFO", "component": "o.e.c.c.CoordinationState", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "cluster UUID set to [HuIFY4U4RMeVvBeL4rpyPw]" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,891-04:00", "level": "INFO", "component": "o.e.c.s.ClusterApplierService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "master node changed {previous [], current [{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{60RTttZ5Rc2TppAYjTVxqw}{*************}{*************:9300}{cdfhilmrstw}]}, term: 1, version: 1, reason: Publication{term=1, version=1}" }
{"type": "server", "timestamp": "2025-06-24T12:13:34,920-04:00", "level": "INFO", "component": "o.e.h.AbstractHttpServerTransport", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "publish_address {*************:9200}, bound_addresses {[::]:9200}", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:34,920-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "started", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:34,976-04:00", "level": "INFO", "component": "o.e.g.GatewayService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "recovered [0] indices into cluster_state", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,436-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [.ml-anomalies-] for index patterns [.ml-anomalies-*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,492-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [.ml-state] for index patterns [.ml-state*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,547-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [.ml-stats] for index patterns [.ml-stats-*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,600-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [.ml-notifications-000002] for index patterns [.ml-notifications-000002]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,655-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [logs-settings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,708-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [synthetics-settings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,747-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [data-streams-mappings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,802-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [metrics-mappings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,840-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [logs-mappings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,870-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [metrics-settings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,901-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [synthetics-mappings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,939-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [.watch-history-13] for index patterns [.watcher-history-13*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:35,971-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [ilm-history] for index patterns [ilm-history-5*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,004-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [.deprecation-indexing-settings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,034-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding component template [.deprecation-indexing-mappings]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,065-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [.slm-history] for index patterns [.slm-history-5*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,095-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [logs] for index patterns [logs-*-*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,128-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [metrics] for index patterns [metrics-*-*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,162-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [synthetics] for index patterns [synthetics-*-*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,191-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [.deprecation-indexing-template] for index patterns [.logs-deprecation.*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,220-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [ml-size-based-ilm-policy]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,255-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [logs]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,283-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [metrics]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,311-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [synthetics]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,339-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [7-days-default]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,373-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [365-days-default]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,417-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [30-days-default]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,455-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [180-days-default]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,484-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [90-days-default]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,510-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [watch-history-ilm-policy]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,538-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [ilm-history-ilm-policy]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,566-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [slm-history-ilm-policy]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,591-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [.deprecation-indexing-ilm-policy]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,618-04:00", "level": "INFO", "component": "o.e.x.i.a.TransportPutLifecycleAction", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index lifecycle policy [.fleet-actions-results-ilm-policy]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,677-04:00", "level": "INFO", "component": "o.e.i.g.GeoIpDownloader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "updating geoip databases", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,678-04:00", "level": "INFO", "component": "o.e.i.g.GeoIpDownloader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "fetching geoip databases overview from [https://geoip.elastic.co/v1/database?elastic_geoip_service_tos=agree]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:36,730-04:00", "level": "INFO", "component": "o.e.l.LicenseService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "license [41a2d8a7-d3ee-4040-b3a2-1d1cbb9186bb] mode [basic] - valid", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:37,744-04:00", "level": "INFO", "component": "o.e.c.m.MetadataCreateIndexService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "[.geoip_databases] creating index, cause [auto(bulk api)], templates [], shards [1]/[0]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:37,931-04:00", "level": "INFO", "component": "o.e.c.r.a.AllocationService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[.geoip_databases][0]]]).", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:38,725-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseNodeService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "retrieve geoip database [GeoLite2-ASN.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb.tmp.gz]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:38,731-04:00", "level": "INFO", "component": "o.e.i.g.GeoIpDownloader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "successfully downloaded geoip database [GeoLite2-ASN.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:38,843-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseNodeService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:44,186-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseNodeService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "retrieve geoip database [GeoLite2-City.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb.tmp.gz]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:44,193-04:00", "level": "INFO", "component": "o.e.i.g.GeoIpDownloader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "successfully downloaded geoip database [GeoLite2-City.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:44,630-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseNodeService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:45,249-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseNodeService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "retrieve geoip database [GeoLite2-Country.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb.tmp.gz]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:45,256-04:00", "level": "INFO", "component": "o.e.i.g.GeoIpDownloader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "successfully downloaded geoip database [GeoLite2-Country.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:13:45,318-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseNodeService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:14,196-04:00", "level": "INFO", "component": "o.e.c.m.MetadataIndexTemplateService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "adding index template [seo_template] for index patterns [seo_*]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,056-04:00", "level": "INFO", "component": "o.e.c.m.MetadataCreateIndexService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "[seo_content] creating index, cause [api], templates [seo_template], shards [1]/[0]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,186-04:00", "level": "INFO", "component": "o.e.c.r.a.AllocationService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_content][0]]]).", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,233-04:00", "level": "INFO", "component": "o.e.c.m.MetadataCreateIndexService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "[seo_analysis] creating index, cause [api], templates [seo_template], shards [1]/[0]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,359-04:00", "level": "INFO", "component": "o.e.c.r.a.AllocationService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_analysis][0]]]).", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,405-04:00", "level": "INFO", "component": "o.e.c.m.MetadataCreateIndexService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "[seo_keywords] creating index, cause [api], templates [seo_template], shards [1]/[0]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,529-04:00", "level": "INFO", "component": "o.e.c.r.a.AllocationService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_keywords][0]]]).", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,575-04:00", "level": "INFO", "component": "o.e.c.m.MetadataCreateIndexService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "[seo_competitors] creating index, cause [api], templates [seo_template], shards [1]/[0]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:15:22,699-04:00", "level": "INFO", "component": "o.e.c.r.a.AllocationService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_competitors][0]]]).", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,593-04:00", "level": "INFO", "component": "o.e.x.m.p.NativeController", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "Native controller process has stopped - no new native processes can be started", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,593-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "stopping ...", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,596-04:00", "level": "INFO", "component": "o.e.x.w.WatcherService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "stopping watch service, reason [shutdown initiated]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,597-04:00", "level": "INFO", "component": "o.e.x.w.WatcherLifeCycleService", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "watcher has stopped and shutdown", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,643-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "stopped", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,643-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "closing ...", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,647-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseReaderLazyLoader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,647-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseReaderLazyLoader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,647-04:00", "level": "INFO", "component": "o.e.i.g.DatabaseReaderLazyLoader", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb]", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
{"type": "server", "timestamp": "2025-06-24T12:31:20,648-04:00", "level": "INFO", "component": "o.e.n.Node", "cluster.name": "ai-seo-king", "node.name": "seo-node-1", "message": "closed", "cluster.uuid": "HuIFY4U4RMeVvBeL4rpyPw", "node.id": "bmhpVyXASJ2JME1Pah6BeQ"  }
