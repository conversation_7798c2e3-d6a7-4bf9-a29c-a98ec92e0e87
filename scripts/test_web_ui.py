#!/usr/bin/env python3
"""
AI SEO 優化王 - Web UI 功能測試腳本
"""

import asyncio
import json
import sys
import os
import time
import requests
from datetime import datetime
from typing import Dict, Any, List

# 添加項目路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend-fastapi'))

try:
    from app.core.elasticsearch import es_client
    from app.core.config import get_settings
except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
    print("請確保已安裝所有依賴")
    sys.exit(1)

settings = get_settings()

class WebUITester:
    """Web UI 功能測試器"""
    
    def __init__(self):
        self.web_ui_url = "http://localhost:8080"
        self.elasticsearch_url = "http://localhost:9200"
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {},
            "overall_status": "unknown"
        }
    
    async def run_all_tests(self):
        """執行所有測試"""
        print("🧪 開始 Web UI 功能測試...")
        print("=" * 60)
        
        # 1. 基礎連接測試
        await self.test_basic_connectivity()
        
        # 2. Elasticsearch 連接測試
        await self.test_elasticsearch_connection()
        
        # 3. Web UI 服務測試
        await self.test_web_ui_service()
        
        # 4. 認證功能測試
        await self.test_authentication()
        
        # 5. 索引管理測試
        await self.test_index_management()
        
        # 6. 搜索功能測試
        await self.test_search_functionality()
        
        # 7. 安全性測試
        await self.test_security_features()
        
        # 生成測試報告
        self.generate_test_report()
    
    async def test_basic_connectivity(self):
        """基礎連接測試"""
        print("1️⃣ 測試基礎連接...")
        
        try:
            # 測試 Elasticsearch 連接
            response = requests.get(self.elasticsearch_url, timeout=10)
            es_status = response.status_code == 200
            
            # 測試 Web UI 連接
            response = requests.get(self.web_ui_url, timeout=10)
            ui_status = response.status_code == 200
            
            self.test_results["tests"]["basic_connectivity"] = {
                "status": "success" if (es_status and ui_status) else "failed",
                "elasticsearch": es_status,
                "web_ui": ui_status
            }
            
            if es_status and ui_status:
                print("   ✅ 基礎連接測試通過")
            else:
                print(f"   ❌ 基礎連接測試失敗 (ES: {es_status}, UI: {ui_status})")
                
        except Exception as e:
            self.test_results["tests"]["basic_connectivity"] = {
                "status": "failed",
                "error": str(e)
            }
            print(f"   ❌ 基礎連接測試失敗: {e}")
    
    async def test_elasticsearch_connection(self):
        """Elasticsearch 連接測試"""
        print("\n2️⃣ 測試 Elasticsearch 連接...")
        
        try:
            await es_client.connect()
            health = await es_client.health_check()
            
            self.test_results["tests"]["elasticsearch_connection"] = {
                "status": "success" if health["healthy"] else "warning",
                "cluster_health": health.get("status"),
                "healthy": health.get("healthy")
            }
            
            if health["healthy"]:
                print(f"   ✅ Elasticsearch 連接正常 (狀態: {health.get('status')})")
            else:
                print(f"   ⚠️ Elasticsearch 連接異常 (狀態: {health.get('status')})")
                
        except Exception as e:
            self.test_results["tests"]["elasticsearch_connection"] = {
                "status": "failed",
                "error": str(e)
            }
            print(f"   ❌ Elasticsearch 連接測試失敗: {e}")
        finally:
            await es_client.close()
    
    async def test_web_ui_service(self):
        """Web UI 服務測試"""
        print("\n3️⃣ 測試 Web UI 服務...")
        
        try:
            # 測試主頁面
            response = requests.get(f"{self.web_ui_url}/index.html", timeout=10)
            main_page = response.status_code == 200
            
            # 測試認證頁面
            response = requests.get(f"{self.web_ui_url}/auth.html", timeout=10)
            auth_page = response.status_code == 200
            
            # 測試配置文件
            response = requests.get(f"{self.web_ui_url}/config.js", timeout=10)
            config_file = response.status_code == 200
            
            self.test_results["tests"]["web_ui_service"] = {
                "status": "success" if (main_page and auth_page and config_file) else "failed",
                "main_page": main_page,
                "auth_page": auth_page,
                "config_file": config_file
            }
            
            if main_page and auth_page and config_file:
                print("   ✅ Web UI 服務測試通過")
            else:
                print(f"   ❌ Web UI 服務測試失敗 (主頁: {main_page}, 認證: {auth_page}, 配置: {config_file})")
                
        except Exception as e:
            self.test_results["tests"]["web_ui_service"] = {
                "status": "failed",
                "error": str(e)
            }
            print(f"   ❌ Web UI 服務測試失敗: {e}")
    
    async def test_authentication(self):
        """認證功能測試"""
        print("\n4️⃣ 測試認證功能...")
        
        try:
            # 這裡我們只能測試頁面是否存在，實際認證需要瀏覽器環境
            response = requests.get(f"{self.web_ui_url}/auth.html", timeout=10)
            auth_page_exists = response.status_code == 200
            
            # 檢查認證頁面是否包含必要元素
            content = response.text
            has_login_form = 'login-form' in content
            has_username_field = 'username' in content
            has_password_field = 'password' in content
            
            self.test_results["tests"]["authentication"] = {
                "status": "success" if (auth_page_exists and has_login_form and has_username_field and has_password_field) else "failed",
                "auth_page_exists": auth_page_exists,
                "has_login_form": has_login_form,
                "has_username_field": has_username_field,
                "has_password_field": has_password_field
            }
            
            if auth_page_exists and has_login_form:
                print("   ✅ 認證功能測試通過")
            else:
                print("   ❌ 認證功能測試失敗")
                
        except Exception as e:
            self.test_results["tests"]["authentication"] = {
                "status": "failed",
                "error": str(e)
            }
            print(f"   ❌ 認證功能測試失敗: {e}")
    
    async def test_index_management(self):
        """索引管理測試"""
        print("\n5️⃣ 測試索引管理功能...")
        
        try:
            await es_client.connect()
            
            # 檢查 SEO 索引是否存在
            indices_response = await es_client.client.cat.indices(format="json")
            existing_indices = [idx["index"] for idx in indices_response]
            
            seo_indices = [settings.ES_SEO_CONTENT_INDEX, settings.ES_SEO_ANALYSIS_INDEX, 
                          settings.ES_SEO_KEYWORDS_INDEX, settings.ES_SEO_COMPETITORS_INDEX]
            
            indices_status = {}
            for index_name in seo_indices:
                indices_status[index_name] = index_name in existing_indices
            
            all_indices_exist = all(indices_status.values())
            
            self.test_results["tests"]["index_management"] = {
                "status": "success" if all_indices_exist else "warning",
                "indices_status": indices_status,
                "total_indices": len(existing_indices)
            }
            
            if all_indices_exist:
                print(f"   ✅ 索引管理測試通過 (找到 {len(seo_indices)} 個 SEO 索引)")
            else:
                missing = [k for k, v in indices_status.items() if not v]
                print(f"   ⚠️ 部分 SEO 索引缺失: {missing}")
                
        except Exception as e:
            self.test_results["tests"]["index_management"] = {
                "status": "failed",
                "error": str(e)
            }
            print(f"   ❌ 索引管理測試失敗: {e}")
        finally:
            await es_client.close()
    
    async def test_search_functionality(self):
        """搜索功能測試"""
        print("\n6️⃣ 測試搜索功能...")
        
        try:
            await es_client.connect()
            
            # 測試基本搜索
            search_query = {"query": {"match_all": {}}, "size": 1}
            
            search_results = {}
            for index_name in [settings.ES_SEO_CONTENT_INDEX, settings.ES_SEO_KEYWORDS_INDEX]:
                try:
                    result = await es_client.search(index_name, search_query, size=1)
                    search_results[index_name] = {
                        "success": True,
                        "total_hits": result["hits"]["total"]["value"]
                    }
                except Exception as e:
                    search_results[index_name] = {
                        "success": False,
                        "error": str(e)
                    }
            
            successful_searches = sum(1 for r in search_results.values() if r["success"])
            
            self.test_results["tests"]["search_functionality"] = {
                "status": "success" if successful_searches > 0 else "failed",
                "search_results": search_results,
                "successful_searches": successful_searches
            }
            
            if successful_searches > 0:
                print(f"   ✅ 搜索功能測試通過 ({successful_searches} 個索引可搜索)")
            else:
                print("   ❌ 搜索功能測試失敗")
                
        except Exception as e:
            self.test_results["tests"]["search_functionality"] = {
                "status": "failed",
                "error": str(e)
            }
            print(f"   ❌ 搜索功能測試失敗: {e}")
        finally:
            await es_client.close()
    
    async def test_security_features(self):
        """安全性測試"""
        print("\n7️⃣ 測試安全功能...")
        
        try:
            # 測試 CORS 設置
            headers = {'Origin': 'http://localhost:8080'}
            response = requests.get(self.elasticsearch_url, headers=headers, timeout=10)
            cors_enabled = 'Access-Control-Allow-Origin' in response.headers
            
            # 測試配置文件安全性
            response = requests.get(f"{self.web_ui_url}/config.js", timeout=10)
            config_accessible = response.status_code == 200
            
            self.test_results["tests"]["security_features"] = {
                "status": "success" if cors_enabled else "warning",
                "cors_enabled": cors_enabled,
                "config_accessible": config_accessible
            }
            
            if cors_enabled:
                print("   ✅ 安全功能測試通過 (CORS 已啟用)")
            else:
                print("   ⚠️ CORS 未正確配置")
                
        except Exception as e:
            self.test_results["tests"]["security_features"] = {
                "status": "failed",
                "error": str(e)
            }
            print(f"   ❌ 安全功能測試失敗: {e}")
    
    def generate_test_report(self):
        """生成測試報告"""
        print("\n" + "=" * 60)
        print("📋 Web UI 測試報告")
        print("=" * 60)
        
        total_tests = len(self.test_results["tests"])
        passed_tests = sum(1 for test in self.test_results["tests"].values() if test["status"] == "success")
        warning_tests = sum(1 for test in self.test_results["tests"].values() if test["status"] == "warning")
        failed_tests = sum(1 for test in self.test_results["tests"].values() if test["status"] == "failed")
        
        print(f"總測試數量: {total_tests}")
        print(f"✅ 通過: {passed_tests}")
        print(f"⚠️ 警告: {warning_tests}")
        print(f"❌ 失敗: {failed_tests}")
        
        # 確定整體狀態
        if failed_tests == 0 and warning_tests == 0:
            self.test_results["overall_status"] = "excellent"
            print("\n🎉 整體狀態: 優秀 - Web UI 功能完美！")
        elif failed_tests == 0:
            self.test_results["overall_status"] = "good"
            print("\n✅ 整體狀態: 良好 - Web UI 功能正常，有輕微警告")
        else:
            self.test_results["overall_status"] = "needs_attention"
            print("\n⚠️ 整體狀態: 需要注意 - 發現問題需要解決")
        
        # 顯示訪問信息
        print("\n🌐 Web UI 訪問信息:")
        print(f"   • 主界面: {self.web_ui_url}/index.html")
        print(f"   • 認證頁面: {self.web_ui_url}/auth.html")
        print(f"   • 默認憑證: admin / aiseo2024")
        print(f"   • Elasticsearch: {self.elasticsearch_url}")
        
        # 保存詳細報告
        report_file = f"web_ui_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細報告已保存至: {report_file}")
        
        return self.test_results["overall_status"] == "excellent"


async def main():
    """主函數"""
    tester = WebUITester()
    
    try:
        await tester.run_all_tests()
        
        # 根據結果設置退出碼
        if tester.test_results["overall_status"] == "excellent":
            sys.exit(0)
        elif tester.test_results["overall_status"] == "good":
            sys.exit(1)
        else:
            sys.exit(2)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 測試被用戶中斷")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 測試過程中發生未預期錯誤: {e}")
        sys.exit(3)


if __name__ == "__main__":
    asyncio.run(main())
